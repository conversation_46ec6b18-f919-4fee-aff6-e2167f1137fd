# Database connection string
# Format: postgresql://username:password@hostname:port/database_name
DATABASE_URL=

# External database connection string for Answer model
# Format: postgresql://username:password@hostname:port/database_name
EXTERNAL_DATABASE_URL=

# OpenAI API Key for embeddings and completions
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=

# Logging level (debug, info, warn, error)
LOG_LEVEL=debug

# AWS S3 Configuration for file storage
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_S3_BUCKET=repd-api-files
