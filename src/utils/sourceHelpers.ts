/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import OpenAI from "openai";
import { Op } from "sequelize";

import { logger } from "@/services/logger";
import { FileLink } from "@/store/models/file_link";
import { SiteLink } from "@/store/models/site_link";
import { config } from "@/config";
import { SourceOverride } from "@/store/models/source_override";
import {
  extractDatesFromContent,
  extractMostRecentDateFromContent,
  calculateContentAge,
  extractPublicationDate
} from "./date-utils";

interface Source {
  url: string;
  name?: string;
  text?: string;
  score: number;
}

/**
 * Extracts sources from search results
 */
export const extractSourcesFromResults = (results: any[]): Source[] => {
  const sourcesWithDuplicates = results.map((row: any) => {
    try {
      // Get basic metadata
      const metadata = row.metadata as Record<string, any>;
      const sourceUrl = metadata.sourceUrl || "";
      const s3Url = metadata.s3Url;
      const score = row.score || row.semantic_score || 1;
      const name = row.content;

      // Determine the best URL to use
      let url = sourceUrl || s3Url;

      // If URL is relative and looks like a file, make it absolute
      if (url && !url.startsWith("http") && url.match(/\.(pdf|doc|docx|xls|xlsx|txt)$/i)) {
        url = `${config.siteUrl.replace(/\/$/, "")}/files/${url}`;
      }

      return { url, name, score };
    } catch (error) {
      // Just return a minimal object on error
      return { url: "", score: 1 };
    }
  });

  // Filter out empty URLs and duplicates in one pass
  return sourcesWithDuplicates
    .filter((source) => source.url) // Remove empty URLs
    .sort((a, b) => a.score - b.score) // Sort by score
    .filter((source, index, self) => index === self.findIndex((s) => s.url === source.url)) // Remove duplicates
    .sort((a, b) => a.score - b.score) // Sort by score
    .slice(0, 5); // Take top 5
};

/**
 * Generates summaries for source content in batch
 */
export const generateSourceSummariesBatch = async (contents: string[]): Promise<string[]> => {
  try {
    if (contents.length === 0) return [];

    const openai = new OpenAI();

    // Prepare messages for batch processing
    // const messages = contents.map((content) => [
    //   {
    //     role: "system",
    //     content:
    //       "You are a summarization assistant. Create a single sentence summary (15 words or less) that describes what this content is about.",
    //   },
    //   {
    //     role: "user",
    //     content: content.substring(0, 2000), // Limit content length to avoid token limits
    //   },
    // ]);

    // Make a single batch request
    const response = await openai.chat.completions.create({
      model: config.completionsModel,
      messages: [
        {
          role: "system",
          content:
            "You are a summarization assistant. Create a single sentence summary (15 words or less) that describes what this content is about.",
        },
        {
          role: "user",
          content: contents[0].substring(0, 6000), // Limit content length to avoid token limits
        },
      ],
      n: contents.length, // Request multiple completions
      temperature: 0.3,
      max_tokens: 60,
      // max_completion_tokens: 60,
    });

    // Extract summaries from response
    return response.choices.map((choice) => choice.message.content?.trim() || "Content summary unavailable");
  } catch (error) {
    logger.warn("Error generating source summaries in batch:", error);
    // Return default summaries for all contents
    return contents.map(() => "Content summary unavailable");
  }
};

/**
 * Prepares source content for summarization
 */
export const prepareSourcesForSummarization = (
  sources: Source[],
  results: Array<{ content: string; metadata: Record<string, any>; score?: number; semantic_score?: number }>,
): {
  contentsToSummarize: string[];
  contentSourceMap: Source[];
} => {
  const contentsToSummarize: string[] = [];
  const contentSourceMap: Source[] = [];

  for (const source of sources) {
    // Find the corresponding row with content
    const row = results.find((r: any) => {
      const metadata = r.metadata as Record<string, any>;
      return metadata.sourceUrl === source.url || metadata.s3Url === source.url;
    });

    if (row && row.content) {
      contentsToSummarize.push(row.content);
      contentSourceMap.push(source);
    }
  }

  return { contentsToSummarize, contentSourceMap };
};

/**
 * Applies summaries to sources
 */
export const applySummariesToSources = (
  sources: Source[],
  contentSourceMap: Source[],
  summaries: string[],
): Source[] => {
  return sources.map((source) => {
    const index = contentSourceMap.findIndex((s) => s.url === source.url);
    if (index !== -1 && index < summaries.length) {
      return {
        ...source,
        text: summaries[index],
      };
    }
    return source;
  });
};

/**
 * Saves summaries to source links in the database
 */
export const saveSummariesToSourceLinks = async (contentSourceMap: Source[], summaries: string[]): Promise<void> => {
  try {
    for (let i = 0; i < contentSourceMap.length; i++) {
      const source = contentSourceMap[i];
      const summary = summaries[i];

      if (source.url && summary) {
        // Find the FileLink or SiteLink associated with this URL
        const fileLink = await FileLink.findOne({ where: { url: source.url } });
        if (fileLink) {
          // Update the FileLink with the summary
          await fileLink.update({
            dataValues: {
              metadata: {
                ...((fileLink.get("metadata") as Record<string, unknown>) || {}),
                summary,
              },
            },
          });
          logger.debug(`Saved summary to FileLink: ${source.url}`);
        } else {
          // Try to find a SiteLink instead
          const siteLink = await SiteLink.findOne({ where: { url: source.url } });
          if (siteLink) {
            await siteLink.update({
              dataValues: {
                metadata: {
                  ...siteLink.getDataValue("metadata"),
                  summary,
                },
              },
            });
            logger.debug(`Saved summary to SiteLink: ${source.url}`);
          }
        }
      }
    }
  } catch (error) {
    logger.warn("Error saving summaries to source links:", error);
    throw error;
  }
};

export const checkForSourceOverrides = async (question: string, clientId: number): Promise<string[]> => {
  try {
    // Convert question to lowercase for case-insensitive matching
    const questionLower = question.toLowerCase();

    // Get all source overrides for this client
    const overrides = await SourceOverride.findAll({
      where: {
        clientId,
        enabled: true,
      },
    });

    // Filter the list of overrides to only include those that match the keywords criteria
    const matchingOverrides = overrides.filter((override) => {
      // Initialize empty string to hold keywords extracted from override
      let keywords: string = "";

      // Check if override has getDataValue method (typical for Sequelize models)
      if (typeof (override as { getDataValue?: unknown }).getDataValue === "function") {
        // Extract keywords using Sequelize's getDataValue method with fallback to empty string
        keywords = (override as unknown as { getDataValue: (key: string) => string }).getDataValue("keywords") || "";
      } else if ("keywords" in override && typeof (override as { keywords?: unknown }).keywords === "string") {
        // Alternative way to extract keywords if the object has a direct keywords property
        keywords = (override as { keywords: string }).keywords;
      }

      // Skip this override if no keywords were found
      if (!keywords) return false;

      if (keywords === "all") {
        return true;
      }

      // Split the keywords string into an array using space as delimiter
      const keywordsArray = keywords.split(" ");

      // Store the total number of keywords for later use in matching logic
      const keywordLength = keywordsArray.length;

      // Find all keywords that appear in the question (case insensitive)
      const questionContainsKeywords = keywordsArray.filter((keyword) => questionLower.includes(keyword.toLowerCase()));

      // Return true if:
      // 1. At least one keyword matches, AND
      // 2. Either there's only one keyword total OR more than 2 keywords match
      return questionContainsKeywords.length >= 1 && (keywordLength === 1 || questionContainsKeywords.length > 2);
    });

    if (matchingOverrides.length === 0) return [];
    return matchingOverrides.map((override) => override.getDataValue("overrideText"));
  } catch (error) {
    logger.warn("Error checking for source overrides:", error);
    return [];
  }
};

/**
 * Debug helper to log source date information
 */
export const logSourceDateInfo = (sources: any[], results: any[]): void => {
  logger.info("=== SOURCE DATE DEBUG INFO ===");

  sources.forEach((source, index) => {
    const url = source.url;
    const matchingResult = results.find((r: any) => {
      const metadata = r.metadata as Record<string, any>;
      return metadata.sourceUrl === url || metadata.s3Url === url;
    });

    if (matchingResult) {
      const metadata = matchingResult.metadata as Record<string, any>;
      logger.info(`Source ${index + 1}: ${url}`);
      logger.info(`  Score: ${source.score}`);
      logger.info(`  Content: ${matchingResult.content.substring(0, 100)}...`);
      logger.info(`  Dates in metadata:`);
      logger.info(`    lastModified: ${metadata.lastModified || "N/A"}`);
      logger.info(`    createdAt: ${metadata.createdAt || "N/A"}`);
      logger.info(`    extractedDate: ${metadata.extractedDate || "N/A"}`);

      // Try to extract dates from content
      const contentDates = extractDatesFromContent(matchingResult.content);
      logger.info(`  Dates in content: ${contentDates.length > 0 ? contentDates.join(", ") : "None found"}`);

      // Calculate age for ranking
      const ageSeconds = calculateContentAge(metadata, matchingResult.content);
      logger.info(`  Age in seconds: ${ageSeconds}`);
      logger.info(`  Age penalty: ${(ageSeconds / 31536000.0) * 0.25}`);
      logger.info(`  Combined score: ${matchingResult.semantic_score + (ageSeconds / 31536000.0) * 0.25}`);
    }
  });

  logger.info("=== END SOURCE DATE DEBUG INFO ===");
};

// Re-export from lib for backward compatibility
export { calculateContentAge };

// Re-export from lib for backward compatibility
export { extractMostRecentDateFromContent };

// Re-export from lib for backward compatibility
export { extractDatesFromContent };

// Re-export from lib for backward compatibility
export { extractPublicationDate };
