#!/usr/bin/env node

import { logger } from "@/services/logger";
import { initDb } from "@/store/instance";
import { answerMigrationService } from "@/services/answers/migrationService";

async function main() {
  try {
    // Parse command line arguments
    const args = process.argv.slice(2);
    const clientIdArg = args.find(arg => arg.startsWith("--clientId="));
    const batchSizeArg = args.find(arg => arg.startsWith("--batchSize="));
    const validateOnlyArg = args.includes("--validate-only");

    const clientId = clientIdArg ? parseInt(clientIdArg.split("=")[1]) : undefined;
    const batchSize = batchSizeArg ? parseInt(batchSizeArg.split("=")[1]) : 100;

    logger.info("Starting answer migration script");
    logger.info(`Client ID: ${clientId || "all"}`);
    logger.info(`Batch size: ${batchSize}`);
    logger.info(`Validate only: ${validateOnlyArg}`);

    // Initialize main database
    await initDb();

    if (validateOnlyArg) {
      // Only validate migration
      const validation = await answerMigrationService.validateMigration(clientId);
      
      if (validation.isValid) {
        logger.info("✅ Migration validation passed");
        process.exit(0);
      } else {
        logger.error("❌ Migration validation failed");
        logger.error(`Source count: ${validation.sourceCount}`);
        logger.error(`Target count: ${validation.targetCount}`);
        process.exit(1);
      }
    } else {
      // Run migration
      const result = await answerMigrationService.migrateAnswerEmbeddings({
        clientId,
        batchSize,
      });

      logger.info("Migration completed:");
      logger.info(`✅ Migrated: ${result.migrated}`);
      logger.info(`❌ Errors: ${result.errors}`);

      // Validate migration
      const validation = await answerMigrationService.validateMigration(clientId);
      
      if (validation.isValid) {
        logger.info("✅ Migration validation passed");
      } else {
        logger.warn("⚠️  Migration validation failed - counts don't match");
        logger.warn(`Source count: ${validation.sourceCount}`);
        logger.warn(`Target count: ${validation.targetCount}`);
      }

      process.exit(result.errors > 0 ? 1 : 0);
    }
  } catch (error) {
    logger.error("Migration script failed:", error);
    process.exit(1);
  }
}

// Handle script execution
if (require.main === module) {
  main();
}

export { main as migrateAnswers };
