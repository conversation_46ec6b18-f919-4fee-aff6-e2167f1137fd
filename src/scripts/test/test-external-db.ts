#!/usr/bin/env node

import { logger } from "@/services/logger";
import { answerService } from "@/services/answers/external/answerService";
import { answerReplacementService } from "@/services/answers/answerReplacementService";

async function testExternalDatabase() {
  try {
    logger.info("Testing external database connection and Answer model...");

    // Test 1: Initialize the service
    logger.info("1. Initializing answer service...");
    await answerService.initialize();
    logger.info("✅ Answer service initialized successfully");

    // Test 2: Create a test answer
    logger.info("2. Creating test answer...");
    const testAnswer = await answerService.createAnswer({
      videoUrl: "https://example.com/test-video.mp4",
      videoUrls: {
        hd: "https://example.com/test-video-hd.mp4",
        sd: "https://example.com/test-video-sd.mp4",
      },
      clientId: 999,
      userId: 1,
      questionId: 1,
      transcription: {
        text: "This is a test transcription for the video answer.",
      },
      isApproved: true,
      isDraft: false,
    });
    logger.info(`✅ Test answer created with ID: ${testAnswer.id}`);

    // Test 3: Find the answer by ID
    logger.info("3. Finding answer by ID...");
    const foundAnswer = await answerService.findAnswerById(testAnswer.id);
    if (foundAnswer) {
      logger.info(`✅ Found answer: ${foundAnswer.videoUrl}`);
    } else {
      throw new Error("Answer not found");
    }

    // Test 4: Find answers by client ID
    logger.info("4. Finding answers by client ID...");
    const clientAnswers = await answerService.findAnswersByClientId(999, {
      limit: 10,
    });
    logger.info(`✅ Found ${clientAnswers.answers.length} answers for client 999`);

    // Test 5: Search answers
    logger.info("5. Searching answers...");
    const searchResults = await answerService.searchAnswers("test", 999);
    logger.info(`✅ Search returned ${searchResults.answers.length} results`);

    // Test 6: Test replacement service
    logger.info("6. Testing answer replacement service...");
    const matchingAnswers = await answerReplacementService.getMatchingAnswers("test", 999, {
      limit: 5,
      onlyApproved: true,
    });
    logger.info(`✅ Found ${matchingAnswers.length} matching answers`);

    // Test 7: Get answer stats
    logger.info("7. Getting answer stats...");
    const stats = await answerReplacementService.getAnswerStats(999);
    logger.info(`✅ Answer stats:`, stats);

    // Test 8: Format answers for response
    logger.info("8. Testing answer formatting...");
    const formattedAnswers = answerReplacementService.formatAnswersForResponse(matchingAnswers);
    logger.info(`✅ Formatted ${formattedAnswers.length} answers for API response`);

    // Test 9: Update the test answer
    logger.info("9. Updating test answer...");
    const updatedAnswer = await answerService.updateAnswer(testAnswer.id, {
      isPinned: true,
      isShared: true,
    });
    if (updatedAnswer) {
      logger.info(`✅ Updated answer - isPinned: ${updatedAnswer.isPinned}, isShared: ${updatedAnswer.isShared}`);
    }

    // Test 10: Clean up - delete the test answer
    logger.info("10. Cleaning up test answer...");
    const deleted = await answerService.deleteAnswer(testAnswer.id);
    if (deleted) {
      logger.info("✅ Test answer deleted successfully");
    } else {
      logger.warn("⚠️  Test answer was not deleted");
    }

    logger.info("🎉 All tests passed! External database setup is working correctly.");
    process.exit(0);
  } catch (error) {
    logger.error("❌ Test failed:", error);
    process.exit(1);
  }
}

// Handle script execution
if (require.main === module) {
  testExternalDatabase();
}

export { testExternalDatabase };
