import { logger } from "@/services/logger";
import { answerService } from "./external/answerService";
import { AnswerAttributes } from "@/store/models/external";

/**
 * Service to replace answer embeddings functionality with the new Answer model
 */
export class AnswerReplacementService {
  private static instance: AnswerReplacementService;

  private constructor() {}

  public static getInstance(): AnswerReplacementService {
    if (!AnswerReplacementService.instance) {
      AnswerReplacementService.instance = new AnswerReplacementService();
    }
    return AnswerReplacementService.instance;
  }

  /**
   * Get matching answers for a question (replaces answer embeddings search)
   * This would typically use vector similarity search in a production system
   */
  public async getMatchingAnswers(
    question: string,
    clientId: number,
    options?: {
      limit?: number;
      includeTranscriptions?: boolean;
      onlyApproved?: boolean;
    }
  ): Promise<AnswerAttributes[]> {
    try {
      logger.info(`Searching for answers matching question: "${question}" for client ${clientId}`);

      // For now, we'll use text search on transcriptions
      // In a production system, you'd want to implement proper vector similarity search
      const searchResults = await answerService.searchAnswers(question, clientId, {
        limit: options?.limit || 10,
      });

      let answers = searchResults.answers;

      // Filter by approval status if requested
      if (options?.onlyApproved) {
        answers = answers.filter(answer => answer.isApproved === true);
      }

      // Filter out answers without transcriptions if requested
      if (options?.includeTranscriptions) {
        answers = answers.filter(answer => 
          answer.transcription && 
          (typeof answer.transcription === 'object' && 'text' in answer.transcription)
        );
      }

      logger.info(`Found ${answers.length} matching answers`);
      return answers;
    } catch (error) {
      logger.error("Error getting matching answers:", error);
      throw error;
    }
  }

  /**
   * Get popular answers for a client (replaces popular answers from embeddings)
   */
  public async getPopularAnswers(
    clientId: number,
    options?: {
      limit?: number;
      onlyApproved?: boolean;
      onlyPinned?: boolean;
    }
  ): Promise<AnswerAttributes[]> {
    try {
      const result = await answerService.findAnswersByClientId(clientId, {
        limit: options?.limit || 20,
        includeApproved: options?.onlyApproved,
      });

      let answers = result.answers;

      // Filter by pinned status if requested
      if (options?.onlyPinned) {
        answers = answers.filter(answer => answer.isPinned === true);
      }

      // Sort by creation date (newest first) - in production you might want to sort by views/likes
      answers.sort((a, b) => {
        const dateA = new Date(a.createdAt || 0).getTime();
        const dateB = new Date(b.createdAt || 0).getTime();
        return dateB - dateA;
      });

      logger.info(`Retrieved ${answers.length} popular answers for client ${clientId}`);
      return answers;
    } catch (error) {
      logger.error("Error getting popular answers:", error);
      throw error;
    }
  }

  /**
   * Get answer statistics for a client
   */
  public async getAnswerStats(clientId: number): Promise<{
    total: number;
    approved: number;
    drafts: number;
    pinned: number;
    withTranscriptions: number;
  }> {
    try {
      const allAnswers = await answerService.findAnswersByClientId(clientId, {
        limit: 10000, // Get all answers for stats
      });

      const answers = allAnswers.answers;

      const stats = {
        total: answers.length,
        approved: answers.filter(a => a.isApproved === true).length,
        drafts: answers.filter(a => a.isDraft === true).length,
        pinned: answers.filter(a => a.isPinned === true).length,
        withTranscriptions: answers.filter(a => 
          a.transcription && 
          typeof a.transcription === 'object' && 
          'text' in a.transcription
        ).length,
      };

      logger.info(`Answer stats for client ${clientId}:`, stats);
      return stats;
    } catch (error) {
      logger.error("Error getting answer stats:", error);
      throw error;
    }
  }

  /**
   * Format answer for API response (similar to how answer embeddings were formatted)
   */
  public formatAnswerForResponse(answer: AnswerAttributes): {
    id: string;
    videoUrl: string;
    videoUrls: Record<string, any>;
    thumbnail?: string;
    transcription?: string;
    duration?: number;
    isApproved: boolean;
    isPinned: boolean;
    createdAt: string;
  } {
    return {
      id: answer.id.toString(),
      videoUrl: answer.videoUrl,
      videoUrls: answer.videoUrls,
      thumbnail: answer.imageUrl,
      transcription: answer.transcription && typeof answer.transcription === 'object' && 'text' in answer.transcription 
        ? (answer.transcription as any).text 
        : undefined,
      duration: answer.videoDuration,
      isApproved: answer.isApproved || false,
      isPinned: answer.isPinned || false,
      createdAt: answer.createdAt?.toISOString() || new Date().toISOString(),
    };
  }

  /**
   * Batch format answers for API response
   */
  public formatAnswersForResponse(answers: AnswerAttributes[]): ReturnType<typeof this.formatAnswerForResponse>[] {
    return answers.map(answer => this.formatAnswerForResponse(answer));
  }
}

// Export singleton instance
export const answerReplacementService = AnswerReplacementService.getInstance();
