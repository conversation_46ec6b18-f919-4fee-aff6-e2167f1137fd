import { Op } from "sequelize";

import { logger } from "@/services/logger";
import { Answer, AnswerAttributes, initExternalDb } from "@/store/models/external";

export class AnswerService {
  private static instance: AnswerService;
  private initialized = false;

  private constructor() {}

  public static getInstance(): AnswerService {
    if (!AnswerService.instance) {
      AnswerService.instance = new AnswerService();
    }
    return AnswerService.instance;
  }

  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      await initExternalDb();
      this.initialized = true;
      logger.info("AnswerService initialized with external database");
    } catch (error) {
      logger.error("Failed to initialize AnswerService:", error);
      throw error;
    }
  }

  public async findAnswersByClientId(
    clientId: number,
    options?: {
      limit?: number;
      offset?: number;
      includeApproved?: boolean;
      includeDrafts?: boolean;
    },
  ): Promise<{ answers: AnswerAttributes[]; total: number }> {
    await this.initialize();

    const whereClause: any = { clientId };

    if (options?.includeApproved !== undefined) {
      whereClause.isApproved = options.includeApproved;
    }

    if (options?.includeDrafts !== undefined) {
      whereClause.isDraft = options.includeDrafts;
    }

    try {
      const { count, rows } = await Answer.findAndCountAll({
        where: whereClause,
        limit: options?.limit || 50,
        offset: options?.offset || 0,
        order: [["createdAt", "DESC"]],
      });

      return {
        answers: rows.map((row) => row.toJSON()),
        total: count,
      };
    } catch (error) {
      logger.error("Error finding answers by client ID:", error);
      throw error;
    }
  }

  public async findAnswerById(id: number): Promise<AnswerAttributes | null> {
    await this.initialize();

    try {
      const answer = await Answer.findByPk(id);
      return answer ? answer.toJSON() : null;
    } catch (error) {
      logger.error("Error finding answer by ID:", error);
      throw error;
    }
  }

  public async createAnswer(answerData: Partial<AnswerAttributes>): Promise<AnswerAttributes> {
    await this.initialize();

    try {
      const answer = await Answer.create(answerData);
      logger.info(`Created new answer with ID: ${answer.getDataValue("id")}`);
      return answer.toJSON();
    } catch (error) {
      logger.error("Error creating answer:", error);
      throw error;
    }
  }

  public async updateAnswer(id: number, updateData: Partial<AnswerAttributes>): Promise<AnswerAttributes | null> {
    await this.initialize();

    try {
      const [updatedRowsCount] = await Answer.update(updateData, {
        where: { id },
      });

      if (updatedRowsCount === 0) {
        return null;
      }

      const updatedAnswer = await Answer.findByPk(id);
      return updatedAnswer ? updatedAnswer.toJSON() : null;
    } catch (error) {
      logger.error("Error updating answer:", error);
      throw error;
    }
  }

  public async deleteAnswer(id: number): Promise<boolean> {
    await this.initialize();

    try {
      const deletedRowsCount = await Answer.destroy({
        where: { id },
      });

      return deletedRowsCount > 0;
    } catch (error) {
      logger.error("Error deleting answer:", error);
      throw error;
    }
  }

  public async searchAnswers(
    searchQuery: string,
    clientId: number,
    options?: {
      limit?: number;
      offset?: number;
    },
  ): Promise<{ answers: AnswerAttributes[]; total: number }> {
    await this.initialize();

    try {
      const whereClause = {
        clientId,
        [Op.or]: [
          {
            transcription: {
              [Op.iLike]: `%${searchQuery}%`,
            },
          },
          {
            transcriptionTranslation: {
              [Op.iLike]: `%${searchQuery}%`,
            },
          },
        ],
      };

      const { count, rows } = await Answer.findAndCountAll({
        where: whereClause,
        limit: options?.limit || 50,
        offset: options?.offset || 0,
        order: [["createdAt", "DESC"]],
      });

      return {
        answers: rows.map((row) => row.toJSON()),
        total: count,
      };
    } catch (error) {
      logger.error("Error searching answers:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const answerService = AnswerService.getInstance();
