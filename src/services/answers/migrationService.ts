import { logger } from "@/services/logger";
import { AnswerEmbedding } from "@/store/models/answer_embedding";
import { Answer, AnswerAttributes, initExternalDb } from "@/store/models/external";

export class AnswerMigrationService {
  private static instance: AnswerMigrationService;

  private constructor() {}

  public static getInstance(): AnswerMigrationService {
    if (!AnswerMigrationService.instance) {
      AnswerMigrationService.instance = new AnswerMigrationService();
    }
    return AnswerMigrationService.instance;
  }

  /**
   * Migrate answer embeddings to the new Answer model in external database
   */
  public async migrateAnswerEmbeddings(options?: {
    batchSize?: number;
    clientId?: number;
  }): Promise<{ migrated: number; errors: number }> {
    await initExternalDb();

    const batchSize = options?.batchSize || 100;
    let migrated = 0;
    let errors = 0;
    let offset = 0;

    logger.info("Starting migration of answer embeddings to external database");

    try {
      while (true) {
        // Fetch batch of answer embeddings
        const whereClause: any = {};
        if (options?.clientId) {
          whereClause.repdClientId = options.clientId;
        }

        const answerEmbeddings = await AnswerEmbedding.findAll({
          where: whereClause,
          limit: batchSize,
          offset,
          order: [["id", "ASC"]],
        });

        if (answerEmbeddings.length === 0) {
          break;
        }

        // Process each answer embedding
        for (const answerEmbedding of answerEmbeddings) {
          try {
            const answerData = this.mapAnswerEmbeddingToAnswer(answerEmbedding);
            
            // Check if answer already exists in external database
            const existingAnswer = await Answer.findOne({
              where: { id: answerData.id },
            });

            if (existingAnswer) {
              // Update existing answer
              await Answer.update(answerData, {
                where: { id: answerData.id },
              });
            } else {
              // Create new answer
              await Answer.create(answerData);
            }

            migrated++;
          } catch (error) {
            logger.error(`Error migrating answer embedding ${answerEmbedding.getDataValue("id")}:`, error);
            errors++;
          }
        }

        offset += batchSize;
        logger.info(`Migrated ${migrated} answers, ${errors} errors so far...`);
      }

      logger.info(`Migration completed: ${migrated} answers migrated, ${errors} errors`);
      return { migrated, errors };
    } catch (error) {
      logger.error("Error during migration:", error);
      throw error;
    }
  }

  /**
   * Map AnswerEmbedding data to Answer model format
   */
  private mapAnswerEmbeddingToAnswer(answerEmbedding: any): Partial<AnswerAttributes> {
    const data = answerEmbedding.toJSON();
    
    return {
      id: data.repdAnswerId || data.supabaseId,
      transcription: data.transcription ? { text: data.transcription } : undefined,
      transcriptionTranslation: data.transcriptionEs ? { es: data.transcriptionEs } : undefined,
      clientId: data.repdClientId,
      // Note: These fields would need to be populated from the original source
      // since they're not available in the answer_embeddings table
      videoUrl: "", // This would need to be fetched from the original source
      videoUrls: {}, // This would need to be fetched from the original source
      userId: 0, // This would need to be fetched from the original source
      questionId: 0, // This would need to be fetched from the original source
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
    };
  }

  /**
   * Validate migration by comparing counts
   */
  public async validateMigration(clientId?: number): Promise<{
    sourceCount: number;
    targetCount: number;
    isValid: boolean;
  }> {
    await initExternalDb();

    try {
      const whereClause: any = {};
      if (clientId) {
        whereClause.repdClientId = clientId;
      }

      const sourceCount = await AnswerEmbedding.count({
        where: whereClause,
      });

      const targetWhereClause: any = {};
      if (clientId) {
        targetWhereClause.clientId = clientId;
      }

      const targetCount = await Answer.count({
        where: targetWhereClause,
      });

      const isValid = sourceCount === targetCount;

      logger.info(`Migration validation: Source=${sourceCount}, Target=${targetCount}, Valid=${isValid}`);

      return {
        sourceCount,
        targetCount,
        isValid,
      };
    } catch (error) {
      logger.error("Error validating migration:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const answerMigrationService = AnswerMigrationService.getInstance();
