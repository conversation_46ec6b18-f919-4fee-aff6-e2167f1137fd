import { Op } from "sequelize";

import { logger } from "@/services/logger";
import { AnswerEmbedding } from "@/store/models/answer_embedding";

const STOP_WORDS = [
  "the",
  "a",
  "an",
  "and",
  "or",
  "but",
  "in",
  "on",
  "at",
  "to",
  "for",
  "of",
  "with",
  "by",
  "is",
  "are",
  "was",
  "were",
  "be",
  "been",
  "being",
  "have",
  "has",
  "had",
  "do",
  "does",
  "did",
  "will",
  "would",
  "could",
  "should",
  "may",
  "might",
  "must",
  "can",
  "this",
  "that",
  "these",
  "those",
  "i",
  "you",
  "he",
  "she",
  "it",
  "we",
  "they",
  "me",
  "him",
  "her",
  "us",
  "them",
];

const MAX_KEYWORD_CANDIDATES = 5;

/**
 * Calculates word overlap between question and transcription text
 * Returns a score between 0 and 1 representing the percentage of question words found in transcription
 */
export const calculateWordOverlap = (question: string, transcription: string): number => {
  if (!question || !transcription) return 0;

  const questionWords = question
    .toLowerCase()
    .replace(/[^\w\s]/g, " ")
    .split(/\s+/)
    .filter((word) => word.length > 2)
    .filter((word) => !STOP_WORDS.includes(word));

  if (questionWords.length === 0) return 0;

  const transcriptionText = transcription.toLowerCase().replace(/[^\w\s]/g, " ");
  const matchingWords = questionWords.filter((word) => transcriptionText.includes(word));
  const overlapScore = matchingWords.length / questionWords.length;

  return overlapScore;
};

/**
 * Gets video answers from the database using AnswerEmbedding model
 */
export const getVideoAnswersFromDatabase = async (clientId: number): Promise<any[]> => {
  try {
    const results = await AnswerEmbedding.findAll({
      where: {
        repdClientId: clientId,
        [Op.or]: [{ question: { [Op.not]: null } }, { transcription: { [Op.not]: null } }],
      },
      order: [["createdAt", "DESC"]],
      raw: true,
    });

    const formattedResults = {
      rows: results.map((row) => ({
        repd_answer_id: row.repdAnswerId,
        question: row.question,
        transcription: row.transcription,
        created_at: row.createdAt,
      })),
    };

    if (formattedResults.rows.length === 0) {
      return [];
    }

    logger.info(`Found ${formattedResults.rows.length} video answers in the database`);

    return formattedResults.rows.map((row) => ({
      id: row.repd_answer_id.toString(),
      question: { text: row.question || "" },
      transcription: { items: [] },
      transcriptionText: row.transcription || "",
      stats: { answeredAt: row.created_at },
    }));
  } catch (error) {
    logger.error("Error getting video answers from database:", error);
    return [];
  }
};

/**
 * Finds matching answers using keyword matching and OpenAI selection
 */
export const findMatchingAnswers = async (question: string, clientId: number): Promise<any[]> => {
  try {
    const videos = await getVideoAnswersFromDatabase(clientId);

    if (!videos || videos.length === 0) {
      logger.info(`No videos found for clientId: ${clientId}`);
      return [];
    }

    logger.info(`Found ${videos.length} total videos for keyword matching`);

    const videosWithScores = videos
      .map((video) => {
        const questionText = video.question?.text || "";
        const transcriptionText = video.transcriptionText || "";

        const questionScore = questionText ? calculateWordOverlap(question, questionText) : 0;
        const transcriptionScore = transcriptionText ? calculateWordOverlap(question, transcriptionText) : 0;
        const overlapScore = Math.max(questionScore, transcriptionScore);

        logger.info(
          `Video ${video.id}: question_score=${questionScore.toFixed(3)}, transcription_score=${transcriptionScore.toFixed(3)}, final_score=${overlapScore.toFixed(3)}`,
        );

        return {
          repd_answer_id: video.id,
          question: questionText,
          transcription: transcriptionText,
          overlapScore,
          source_type: "answer",
          created_at: video.stats?.answeredAt || new Date().toISOString(),
        };
      })
      .filter((video) => video.overlapScore > 0)
      .sort((a, b) => b.overlapScore - a.overlapScore)
      .slice(0, MAX_KEYWORD_CANDIDATES);

    logger.info(`Found ${videos.length} total videos, ${videosWithScores.length} with keyword overlap`);

    if (videosWithScores.length === 0) {
      return [];
    }

    videosWithScores.forEach((video, index) => {
      logger.info(
        `Candidate ${index + 1}: Video ${video.repd_answer_id}, overlap_score=${video.overlapScore.toFixed(3)}`,
      );
    });

    const selectedAnswer = await selectBestAnswerWithAI(question, videosWithScores);
    return selectedAnswer ? [selectedAnswer] : [];
  } catch (error) {
    logger.error("Error finding matching answers:", error);
    return [];
  }
};
