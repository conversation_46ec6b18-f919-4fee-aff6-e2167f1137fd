import OpenAI from "openai";
import { logger } from "@/services/logger";
import { config } from "@/config";
import { videoAnswerSelectionPrompt } from "@/prompts/video_answer_selection";

/**
 * Uses OpenAI to select the most relevant answer from candidates
 */
export const selectBestAnswerWithAI = async (question: string, candidateAnswers: any[]): Promise<any | null> => {
  if (candidateAnswers.length === 0) {
    return null;
  }

  try {
    const openai = new OpenAI();

    const candidatesText = candidateAnswers
      .map((answer, index) => {
        return `Answer ${index + 1}:
Title: ${answer.question || "No title"}
Transcript: ${answer.transcription || "No transcript"}
Answer ID: ${answer.repd_answer_id}
---`;
      })
      .join("\n\n");

    const prompt = videoAnswerSelectionPrompt(question, candidatesText);

    const response = await openai.chat.completions.create({
      model: config.completionsModel,
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      // GPT-4:
      temperature: 0.1,
      max_tokens: 50,
      // GPT-5:
      // max_completion_tokens: 50,
    });

    const aiResponse = response.choices[0].message.content?.trim();

    if (!aiResponse || aiResponse === "NONE") {
      logger.info(`OpenAI determined no relevant answers for question: "${question}"`);
      return null;
    }

    const selectedAnswerId = aiResponse;
    const selectedAnswer = candidateAnswers.find((answer) => answer.repd_answer_id?.toString() === selectedAnswerId);

    if (selectedAnswer) {
      logger.info(`OpenAI selected answer ${selectedAnswerId} for question: "${question}"`);
      return selectedAnswer;
    } else {
      logger.warn(`OpenAI selected answer ID ${selectedAnswerId} but it wasn't found in candidates`);
      return null;
    }
  } catch (error) {
    logger.error("Error using OpenAI to select best answer:", error);
    return candidateAnswers[0] || null;
  }
};

/**
 * Converts transcription items to plain text
 */
export const transcriptionToText = (transcription: any): string => {
  if (!transcription || !transcription.items) {
    return "";
  }

  return transcription.items
    .map((item: any) => {
      if (item.alternatives && item.alternatives.length > 0) {
        return item.alternatives[0].content;
      }
      return "";
    })
    .join(" ")
    .trim();
};