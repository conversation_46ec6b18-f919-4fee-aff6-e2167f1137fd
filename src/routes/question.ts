import express, { Request, Response, Router } from "express";
import { z } from "zod";
import { QueryTypes } from "sequelize";

import { logger } from "../services/logger";
import { answerUserQuestion } from "../services/openai";
import { sequelize } from "../store/instance";

const questionRouter: Router = Router();

const QuestionSchema = z.object({
  question: z.string().min(1, "Question cannot be empty"),
  clientId: z.union([z.string(), z.number()]).optional(),
  client: z.object({ id: z.union([z.string(), z.number()]) }).optional(),
  internalType: z.string().nullable().optional(),
  isTeleprompter: z.boolean().optional().default(false),
});

questionRouter.post(
  "/answer/:clientId",
  // @ts-ignore-error
  async (req: Request, res: Response, next: express.NextFunction) => {
    try {
      const {
        question,
        client,
        clientId: cId,
        internalType = null,
        isTeleprompter = false,
      } = QuestionSchema.parse(req.body);

      const clientId = Number(cId || req.query.clientId || req.params.clientId || client?.id);
      const userIdRaw = req.query.userId || req.params.userId;
      const userId = userIdRaw ? Number(userIdRaw) : null;

      if (isNaN(clientId)) {
        return res.status(400).json({ error: "Invalid clientId, must be a number" });
      }

      // Validate userId if provided
      if (userIdRaw && (isNaN(userId!) || userId! <= 0)) {
        return res.status(400).json({ error: "Invalid userId, must be a positive number" });
      }

      const answer = await answerUserQuestion(
        question.replace(/'s/g, " is"),
        clientId,
        internalType,
        isTeleprompter,
        userId,
      );

      return res.json({ answer });
    } catch (error) {
      logger.error("Error answering user question:", error);

      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid request body", details: error.errors });
      }

      return res.status(500).json({ error: "Failed to process question" });
    }
  },
);

// Route to fetch question embeddings for a client
questionRouter.get(
  "/question-embeddings/:clientId",
  // @ts-ignore-error
  async (req: Request, res: Response, next: express.NextFunction) => {
    try {
      const clientId = Number(req.params.clientId);

      if (isNaN(clientId)) {
        return res.status(400).json({ error: "Invalid clientId, must be a number" });
      }

      const schemaName = `client_${clientId}`;

      const query = `
        SELECT id, question, answer, related_embeddings, question_embedding_token_usage,
               answer_token_usage, search_time, get_question_embedding_time, answering_time,
               sources, created_at, updated_at
        FROM ${schemaName}.question_embeddings
        WHERE client_id = ${clientId}
        ORDER BY created_at DESC
        LIMIT 100
      `;

      const results = await sequelize.query(query, {
        type: QueryTypes.SELECT,
      });

      return res.json({ data: results });
    } catch (error) {
      logger.error("Error fetching question embeddings:", error);
      return res.status(500).json({ error: "Failed to fetch question embeddings" });
    }
  },
);

// Route to fetch sites for a client
questionRouter.get(
  "/sites/:clientId",
  // @ts-ignore-error
  async (req: Request, res: Response, next: express.NextFunction) => {
    try {
      const clientId = Number(req.params.clientId);

      if (isNaN(clientId)) {
        return res.status(400).json({ error: "Invalid clientId, must be a number" });
      }

      const schemaName = `client_${clientId}`;

      const query = `
        SELECT id, url, name, enabled, client_id, last_scraped_at, schema, requires_javascript,
               created_at, updated_at
        FROM ${schemaName}.sites
        WHERE client_id = ${clientId}
        ORDER BY created_at DESC
      `;

      const results = await sequelize.query(query, {
        type: QueryTypes.SELECT,
      });

      return res.json({ data: results });
    } catch (error) {
      logger.error("Error fetching sites:", error);
      return res.status(500).json({ error: "Failed to fetch sites" });
    }
  },
);

// Route to fetch site links for a client
questionRouter.get(
  "/site-links/:clientId",
  // @ts-ignore-error
  async (req: Request, res: Response, next: express.NextFunction) => {
    try {
      const clientId = Number(req.params.clientId);

      if (isNaN(clientId)) {
        return res.status(400).json({ error: "Invalid clientId, must be a number" });
      }

      const schemaName = `client_${clientId}`;

      const query = `
        SELECT id, url, site_id, status, last_scraped_at, created_at
        FROM ${schemaName}.site_links
        WHERE site_id IN (SELECT id FROM sites WHERE client_id = ${clientId})
        ORDER BY created_at DESC
      `;

      const results = await sequelize.query(query, {
        type: QueryTypes.SELECT,
      });

      return res.json({ data: results });
    } catch (error) {
      logger.error("Error fetching site links:", error);
      return res.status(500).json({ error: "Failed to fetch site links" });
    }
  },
);

// Route to fetch all sites across all clients
questionRouter.get(
  "/sites",
  // @ts-ignore-error
  async (req: Request, res: Response, next: express.NextFunction) => {
    try {
      // Parse pagination parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 5;
      const offset = (page - 1) * limit;

      // Get total count for pagination metadata
      const countQuery = `SELECT COUNT(*) as total FROM sites`;
      const countResult = (await sequelize.query(countQuery, {
        type: QueryTypes.SELECT,
      })) as [{ total: string }];
      const total = parseInt(countResult[0].total);

      // Main query with pagination
      const query = `
        SELECT id, url, name, enabled, client_id, last_scraped_at, schema, requires_javascript,
               created_at, updated_at
        FROM sites
        ORDER BY client_id ASC, created_at DESC
        LIMIT $1 OFFSET $2
      `;

      const results = await sequelize.query(query, {
        bind: [limit, offset],
        type: QueryTypes.SELECT,
      });

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      return res.json({
        data: results,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNextPage,
          hasPrevPage,
        },
      });
    } catch (error) {
      logger.error("Error fetching all sites:", error);
      return res.status(500).json({ error: "Failed to fetch all sites" });
    }
  },
);

// Route to fetch all site links across all clients
questionRouter.get(
  "/site-links",
  // @ts-ignore-error
  async (req: Request, res: Response, next: express.NextFunction) => {
    try {
      const siteId = req.query.site_id as string;

      let query: string;
      let queryParams: any[] = [];

      if (siteId) {
        // Filter by specific site_id
        query = `
          SELECT sl.status
          FROM site_links sl
          JOIN sites s ON sl.site_id = s.id
          WHERE sl.site_id = $1
          ORDER BY sl.created_at DESC
        `;
        queryParams = [siteId];
      } else {
        // Return all site links if no site_id specified
        query = `
          SELECT sl.status
          FROM site_links sl
          JOIN sites s ON sl.site_id = s.id
          ORDER BY s.client_id ASC, sl.created_at DESC
        `;
      }

      const results = await sequelize.query(query, {
        bind: queryParams,
        type: QueryTypes.SELECT,
      });

      return res.json({ data: results });
    } catch (error) {
      logger.error("Error fetching site links:", error);
      return res.status(500).json({ error: "Failed to fetch site links" });
    }
  },
);

export { questionRouter };
