import express from "express";
import { logger } from "@/services/logger";
import { answerService } from "@/services/answers/external/answerService";

const router = express.Router();

// GET /api/answers - Get answers by client ID
router.get("/", async (req, res) => {
  try {
    const clientId = parseInt(req.query.clientId as string);
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;
    const includeApproved = req.query.includeApproved === "true";
    const includeDrafts = req.query.includeDrafts === "true";

    if (!clientId) {
      return res.status(400).json({ error: "clientId is required" });
    }

    const result = await answerService.findAnswersByClientId(clientId, {
      limit,
      offset,
      includeApproved,
      includeDrafts,
    });

    res.json({
      success: true,
      data: result.answers,
      total: result.total,
      pagination: {
        limit,
        offset,
        hasMore: offset + limit < result.total,
      },
    });
  } catch (error) {
    logger.error("Error fetching answers:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// GET /api/answers/:id - Get answer by ID
router.get("/:id", async (req, res) => {
  try {
    const id = parseInt(req.params.id);

    if (!id) {
      return res.status(400).json({ error: "Invalid answer ID" });
    }

    const answer = await answerService.findAnswerById(id);

    if (!answer) {
      return res.status(404).json({ error: "Answer not found" });
    }

    res.json({
      success: true,
      data: answer,
    });
  } catch (error) {
    logger.error("Error fetching answer:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// POST /api/answers - Create new answer
router.post("/", async (req, res) => {
  try {
    const answerData = req.body;

    // Validate required fields
    if (!answerData.videoUrl || !answerData.clientId || !answerData.userId || !answerData.questionId) {
      return res.status(400).json({ 
        error: "Missing required fields: videoUrl, clientId, userId, questionId" 
      });
    }

    const answer = await answerService.createAnswer(answerData);

    res.status(201).json({
      success: true,
      data: answer,
    });
  } catch (error) {
    logger.error("Error creating answer:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// PUT /api/answers/:id - Update answer
router.put("/:id", async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const updateData = req.body;

    if (!id) {
      return res.status(400).json({ error: "Invalid answer ID" });
    }

    const answer = await answerService.updateAnswer(id, updateData);

    if (!answer) {
      return res.status(404).json({ error: "Answer not found" });
    }

    res.json({
      success: true,
      data: answer,
    });
  } catch (error) {
    logger.error("Error updating answer:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// DELETE /api/answers/:id - Delete answer
router.delete("/:id", async (req, res) => {
  try {
    const id = parseInt(req.params.id);

    if (!id) {
      return res.status(400).json({ error: "Invalid answer ID" });
    }

    const deleted = await answerService.deleteAnswer(id);

    if (!deleted) {
      return res.status(404).json({ error: "Answer not found" });
    }

    res.json({
      success: true,
      message: "Answer deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting answer:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// GET /api/answers/search - Search answers
router.get("/search", async (req, res) => {
  try {
    const query = req.query.q as string;
    const clientId = parseInt(req.query.clientId as string);
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    if (!query || !clientId) {
      return res.status(400).json({ error: "Query and clientId are required" });
    }

    const result = await answerService.searchAnswers(query, clientId, {
      limit,
      offset,
    });

    res.json({
      success: true,
      data: result.answers,
      total: result.total,
      pagination: {
        limit,
        offset,
        hasMore: offset + limit < result.total,
      },
    });
  } catch (error) {
    logger.error("Error searching answers:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

export { router as answersRouter };
