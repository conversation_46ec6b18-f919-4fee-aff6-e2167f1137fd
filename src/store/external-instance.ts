import { Sequelize } from "sequelize";

import { logger } from "@/services/logger";
import { config } from "@/config";

let isInitialized = false;

export const externalSequelize = new Sequelize(config.externalDatabaseUrl, {
  dialect: "postgres",

  dialectOptions: {
    ssl:
      config.nodeEnv === "production"
        ? {
            rejectUnauthorized: false,
          }
        : false,
  },

  // Replace the current logging with a conditional log
  logging: config.logSQL ? (msg) => logger.debug(msg, { source: "external-db" }) : false,

  define: {
    timestamps: true,
    underscored: true,
  },

  sync: {
    alter: config.nodeEnv === "production" ? false : { drop: false },
  },
});

export const initExternalDb = async () => {
  if (isInitialized) {
    return;
  }

  try {
    await externalSequelize.authenticate();
    logger.info("External database connection established");

    const { initExternalModels } = await import("@/store/models/external/init");
    await initExternalModels();

    isInitialized = true;
  } catch (error) {
    logger.error("Unable to connect to the external database:", error);
    throw error;
  }
};
