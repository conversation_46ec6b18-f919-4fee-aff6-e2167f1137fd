import { DataTypes, Model } from "sequelize";
import { externalSequelize } from "@/store/external-instance";

export interface AnswerAttributes {
  id: number;
  imageUrl?: string;
  videoUrl: string;
  videoUrls: Record<string, any>;
  uploadId?: string;
  videoKey?: string;
  videoDuration?: number;
  subtitles?: Record<string, any>;
  transcription?: Record<string, any>;
  transcriptionTranslation?: Record<string, any>;
  userId: number;
  clientId: number;
  questionId: number;
  isDraft?: boolean;
  isApproved?: boolean;
  isDenied?: boolean;
  isShared?: boolean;
  isPinned?: boolean;
  endDate?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export type AnswerModel = Model<AnswerAttributes, Partial<AnswerAttributes>>;

export const Answer = externalSequelize.define<AnswerModel>(
  "answer",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    imageUrl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: "image_url",
    },
    videoUrl: {
      type: DataTypes.STRING,
      allowNull: false,
      field: "video_url",
    },
    videoUrls: {
      type: DataTypes.JSON,
      allowNull: false,
      field: "video_urls",
    },
    uploadId: {
      type: DataTypes.STRING,
      allowNull: true,
      field: "upload_id",
    },
    videoKey: {
      type: DataTypes.STRING,
      allowNull: true,
      field: "video_key",
    },
    videoDuration: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: "video_duration",
    },
    subtitles: {
      type: DataTypes.JSON,
      allowNull: true,
      field: "subtitles",
    },
    transcription: {
      type: DataTypes.JSONB,
      allowNull: true,
      field: "transcription",
    },
    transcriptionTranslation: {
      type: DataTypes.JSONB,
      allowNull: true,
      field: "transcription_translation",
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: "user_id",
    },
    clientId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: "client_id",
    },
    questionId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: "question_id",
    },
    isDraft: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      field: "is_draft",
    },
    isApproved: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      field: "is_approved",
    },
    isDenied: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      field: "is_denied",
    },
    isShared: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      field: "is_shared",
    },
    isPinned: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      field: "is_pinned",
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: "end_date",
    },
  },
  {
    tableName: "answers",
    timestamps: true,
    underscored: true,
  },
);
