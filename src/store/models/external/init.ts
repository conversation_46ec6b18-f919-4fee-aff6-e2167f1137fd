import { logger } from "@/services/logger";
import { config } from "@/config";

// Import external models
import { Answer } from "./answer";

export async function initExternalModels(): Promise<boolean> {
  try {
    logger.info("Initializing external database models...");

    // Define any associations here if needed
    // For example, if you have User and Question models in the external DB:
    // Answer.belongsTo(User, { foreignKey: "userId", as: "user" });
    // Answer.belongsTo(Question, { foreignKey: "questionId", as: "question" });

    const modelsToSync = [
      Answer,
    ];

    for (const model of modelsToSync) {
      await model.sync({
        alter: config.nodeEnv === "production" ? false : { drop: false },
      });
    }

    logger.info("External database models synchronized");

    return true;
  } catch (error) {
    logger.error("Error setting up external database:", error);
    process.exit(1);
  }
}
