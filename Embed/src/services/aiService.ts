import { API_BASE_URL } from '../config/environment';
import { Answer } from '@/types/answer';

interface AiServiceRequest {
  question: string;
  clientId: string;
  userId: string | null;
  isInternal?: boolean;
}

interface AiServiceResponse {
  statusCode: number;
  classification: string;
  marvinAnswers: {
    answer: any;
    marvinAnswers: {
      response_text: string; sources: Array<{
        url: string;
        score: number;
      }>;
    };
    matchingAnswers: string[]; // Changed from Answer[] to string[] (IDs)
    response_text: string;
    sources: Array<{
      url: string;
      score: number;
    }>;
  };
  matchingAnswers: Answer[];
}

export async function getAnswerFromAI(
  question: string,
  clientId: string,
  userId: string | null,
  isInternal?: boolean
): Promise<AiServiceResponse['marvinAnswers']> {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    // // Check if client is Myrtle Beach and Tallahasse
    const isMyrtleBeach = clientId === '299';
    const isTallahassee = clientId === '301';

    if (!isInternal && (isMyrtleBeach || isTallahassee)) {
      isInternal = true;
    }

    const response = await fetch(`${API_BASE_URL}/ai-service`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify({
        question,
        clientId,
        userId,
        isInternal
      } as AiServiceRequest)
    });

    if (!response.ok) {
      throw new Error('Network response was not ok');
    }

    const data: AiServiceResponse = await response.json();

    return data.marvinAnswers;
  } catch (error) {
    console.error('Error fetching answer:', error);
    throw error;
  }
}
