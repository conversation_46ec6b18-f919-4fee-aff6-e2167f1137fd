import { API_BASE_URL } from '../config/environment';
import { Answer, ApiAnswerResponse, Transcription, TranscriptionTranslations } from '../interfaces/answer.interface';

// Stop words to filter out when analyzing word overlap (same as in PopularAnswers.tsx)
const STOP_WORDS = [
  'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'about', 'as',
  'what', 'is', 'are', 'where', 'when', 'how', 'why', 'can', 'do', 'does', 'which', 'who', 'whom', 'whose',
  'a', 'an', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'will', 'would', 'could', 'should',
  'this', 'that', 'these', 'those', 'there', 'here', 'then', 'than', 'from', 'into', 'out', 'up', 'down'
];

/**
 * Calculates keyword relevance score between search query and video content
 * Returns a score between 0 and 1+ representing keyword match quality
 * Scores above 1 indicate high relevance with exact matches and phrase matches
 */
const calculateKeywordRelevance = (searchQuery: string, questionText: string, transcription?: any): number => {
  if (!searchQuery || !questionText) return 0;

  // Normalize and extract meaningful words from search query
  const queryWords = searchQuery
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2) // Filter out very short words
    .filter(word => !STOP_WORDS.includes(word));

  if (queryWords.length === 0) return 0;

  // Combine question text and transcription for matching
  let searchableText = questionText.toLowerCase().replace(/[^\w\s]/g, ' ');

  // Add transcription text if available
  if (transcription) {
    let transcriptionText = '';

    // Try audio_segments first (preferred format)
    if (transcription.audio_segments && transcription.audio_segments.length > 0) {
      transcriptionText = transcription.audio_segments
        .map((segment: any) => segment.transcript || '')
        .join(' ');
    }
    // Fallback to items format
    else if (transcription.items && transcription.items.length > 0) {
      transcriptionText = transcription.items
        .filter((item: any) => item.type === 'pronunciation')
        .map((item: any) => item.alternatives[0]?.content || '')
        .join(' ');
    }

    if (transcriptionText) {
      searchableText += ' ' + transcriptionText.toLowerCase().replace(/[^\w\s]/g, ' ');
    }
  }

  let totalScore = 0;
  let maxPossibleScore = queryWords.length;

  // Check for exact phrase match (highest weight)
  const normalizedQuery = searchQuery.toLowerCase().replace(/[^\w\s]/g, ' ').trim();
  if (searchableText.includes(normalizedQuery)) {
    totalScore += queryWords.length * 2; // Double weight for exact phrase match
    maxPossibleScore += queryWords.length; // Adjust max possible score
  }

  // Check for individual word matches
  queryWords.forEach(word => {
    if (searchableText.includes(word)) {
      totalScore += 1;

      // Bonus for exact word boundaries (more precise matches)
      const wordBoundaryRegex = new RegExp(`\\b${word}\\b`, 'g');
      const exactMatches = (searchableText.match(wordBoundaryRegex) || []).length;
      if (exactMatches > 0) {
        totalScore += 0.5; // Bonus for exact word boundary matches
        maxPossibleScore += 0.5;
      }

      // Bonus for multiple occurrences
      const allMatches = (searchableText.match(new RegExp(word, 'g')) || []).length;
      if (allMatches > 1) {
        totalScore += Math.min(allMatches - 1, 2) * 0.25; // Diminishing returns for frequency
        maxPossibleScore += Math.min(allMatches - 1, 2) * 0.25;
      }
    }
  });

  // Check for partial matches (substring matching for longer words)
  queryWords.forEach(word => {
    if (word.length >= 4 && !searchableText.includes(word)) {
      // Look for words that contain this word as a substring
      const partialMatches = searchableText.split(/\s+/).filter(textWord =>
        textWord.length >= word.length && textWord.includes(word)
      );
      if (partialMatches.length > 0) {
        totalScore += 0.3; // Lower weight for partial matches
        maxPossibleScore += 0.3;
      }
    }
  });

  // Normalize score (can be > 1 for highly relevant matches)
  const relevanceScore = totalScore / queryWords.length;
  return relevanceScore;
};

export async function getVideoAnswers(clientId: string, userId: string | null, searchQuery?: string): Promise<Answer[]> {
  const token = localStorage.getItem('token');
  if (!token) return [];

  const queryParams = new URLSearchParams();
  queryParams.append('clientId', clientId);
  if (userId) {
    queryParams.append('userId', userId);
  }

  const response = await fetch(`${API_BASE_URL}/answers?${queryParams.toString()}`, {
    headers: {
      'Accept': 'application/json',
      'Authorization': `${token}`
    }
  });

  if (!response.ok) return [];

  const apiResponse: ApiAnswerResponse = await response.json();
  
  // Filter for videos that are both enabled and published
  const publishedVideos = apiResponse.data.filter(item => 
    item.enabled && 
    !item.isDenied &&
    !item.isDraft &&
    !item.imageUrl.match(/empty.(jpg|jpeg)/)
    // item.isApproved === true
  );
  
  // Sort videos based on search query or default sorting
  const sortedVideos = publishedVideos.sort((a, b) => {
    if (searchQuery) {
      // When searching, prioritize keyword relevance
      const relevanceA = calculateKeywordRelevance(searchQuery, a.question.text, a.transcription);
      const relevanceB = calculateKeywordRelevance(searchQuery, b.question.text, b.transcription);

      // Primary sort: by relevance score (higher relevance first)
      if (relevanceA !== relevanceB) {
        return relevanceB - relevanceA;
      }

      // Secondary sort: pinned items (if relevance is equal)
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // Tertiary sort: creation date (newest first)
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    } else {
      // Default sorting when no search query
      // First sort by isPinned (pinned items first)
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // Then sort by creation date (newest first)
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
  });
  
  return sortedVideos.map(item => ({
    id: item.id, // Ensure this is a string to match repd_answer_id
    thumbnail: item.imageUrl,
    videoUrl: item.videoUrl,
    videoUrls: item.videoUrls, // Add this line to include videoUrls
    enabled: item.enabled,
    title: item.question.text,
    stats: {
      votes: item.question.votes,
      answeredAt: item.createdAt
    },
    question: {
      text: item.question.text,
      translations: item.question.translations,
      originalLanguage: item.question.originalLanguage,
      user: {
        firstName: item.question.user.firstName,
        lastName: item.question.user.lastName,
        location: item.question.user.location
      }
    },
    showTranscribedSubtitles: true,
    transcription: item.transcription || { items: [] },
    transcriptionTranslation: item.transcriptionTranslation || {},
    votes: item.votes,
    liked: item.liked,
    likes: item.likes,
    isPinned: item.isPinned
  }));
}

export async function getVideoTranscription(videoId: string): Promise<{
  transcription: Transcription,
  transcriptionTranslation: TranscriptionTranslations
}> {
  const token = localStorage.getItem('token');
  if (!token) {
    return {
      transcription: { items: [] },
      transcriptionTranslation: {}
    };
  }

  try {
    const response = await fetch(`${API_BASE_URL}/videos/${videoId}/transcription`, {
      headers: {
        'Accept': 'application/json',
        'Authorization': `${token}`
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch transcription');
    }

    const data = await response.json();
    console.log('Raw transcription API response:', data);
    
    // Check the structure of the API response
    const transcription = data.transcription || { items: [] };
    
    // The translations might be nested differently in the API response
    // Try different possible structures
    let translations = {};
    if (data.translations) {
      translations = data.translations;
    } else if (data.transcriptionTranslation) {
      translations = data.transcriptionTranslation;
    } else if (data.transcriptionTranslations) {
      translations = data.transcriptionTranslations;
    }
    
    console.log('Processed transcription data:', {
      transcription,
      translationLanguages: Object.keys(translations)
    });
    
    return {
      transcription,
      transcriptionTranslation: translations
    };
  } catch (error) {
    console.error('Failed to fetch video transcription:', error);
    return {
      transcription: { items: [] },
      transcriptionTranslation: {}
    };
  }
}
