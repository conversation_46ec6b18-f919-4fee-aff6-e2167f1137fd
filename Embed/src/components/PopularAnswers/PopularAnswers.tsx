
import { useState, useEffect, useMemo, useRef } from 'react';
import { Answer } from '../../interfaces/answer.interface';
import { getVideoAnswers } from '../../services/answerService';
import { VideoCard } from '../VideoCard/VideoCard';
import { useLanguage } from '../../context/LanguageContext';
import { useAi } from '../../context/AiContext';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import styles from './PopularAnswers.module.css';
import classNames from 'classnames';
import { clientIdToName } from '../../config/clientLogoConfig';
import { normalizeId } from '../../utils/idUtils';
import { ClientInterface } from '../../interfaces/client.interface';



interface PopularAnswersProps {
  onVideoSelect: (answer: Answer) => void;
  question?: string;
  titleKey?: 'popularAnswers' | 'relatedAnswers';
  isQuestionFormExpanded?: boolean;
  client?: ClientInterface | null;
}

export function PopularAnswers({
  onVideoSelect,
  question,
  titleKey = 'popularAnswers',
  isQuestionFormExpanded = false,
  client = null
}: PopularAnswersProps) {
  const { matchingAnswers, isLoading: aiLoading } = useAi();
  const [popularAnswers, setPopularAnswers] = useState<Answer[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useLanguage();
  const [initialLoadDone, setInitialLoadDone] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [isHovering, setIsHovering] = useState(false);
  const [clientSubdomain, setClientSubdomain] = useState('');

  const clientId = localStorage.getItem('clientId');

  // Get client name from URL or localStorage
  useEffect(() => {
    // Extract client name from URL path
    const pathMatch = window.location.pathname.match(/\/([^\/]+)/);
    const clientNameFromUrl = pathMatch ? pathMatch[1].toLowerCase() : null;

    // Get client ID from localStorage
    const clientId = localStorage.getItem('clientId');

    // Determine subdomain: first try from URL, then from client ID mapping using centralized mapping
    const subdomain = clientNameFromUrl ||
                     (clientId && clientIdToName[clientId]) ||
                     'arlington';

    setClientSubdomain(subdomain);
  }, []);

  // Load answers with optional search query
  useEffect(() => {
    const loadAnswers = async () => {
      try {
        setIsLoading(true);
        const clientId = localStorage.getItem('clientId');
        const userId = localStorage.getItem('userId');

        if (!clientId || !userId) {
          return;
        }

        // Pass search query to service for keyword-based sorting
        const searchQueryForService = (titleKey === 'relatedAnswers' && question) ? question : undefined;
        const answers = await getVideoAnswers(clientId, userId, searchQueryForService);
        setPopularAnswers(answers);
        setInitialLoadDone(true);
      } catch (err) {
        console.error('Failed to load answers:', err);
      } finally {
        setIsLoading(false);
      }
    };

    // Load answers when component mounts or when question changes for related answers
    if (!initialLoadDone || (titleKey === 'relatedAnswers' && question)) {
      loadAnswers();
    }
  }, [initialLoadDone, titleKey, question]);



  const displayAnswers = useMemo(() => {
    // For popular answers (no search), just return the service-sorted results
    if (titleKey === 'popularAnswers') {
      return popularAnswers;
    }

    // For related answers, we need to handle the matching logic
    if (titleKey === 'relatedAnswers' && question) {
      // If we have matching answers from AI, prioritize those
      if (matchingAnswers?.length > 0 && popularAnswers.length > 0) {
        // Extract matching IDs
        const matchingIds = new Set<string>();
        matchingAnswers.forEach(ma => {
          const rawId = ma.repd_answer_id;
          if (rawId) {
            const normalizedId = normalizeId(rawId);
            if (normalizedId) {
              matchingIds.add(normalizedId);
            }
          }
        });

        // Find matched answers
        const matchedAnswers: Answer[] = [];
        const remainingAnswers: Answer[] = [];

        popularAnswers.forEach(answer => {
          const answerId = normalizeId(answer.id);
          if (matchingIds.has(answerId)) {
            matchedAnswers.push(answer);
          } else {
            remainingAnswers.push(answer);
          }
        });

        // Return matched answers first, then remaining
        return [...matchedAnswers, ...remainingAnswers];
      }

      // If no matching answers yet, return all popular answers (service already sorted by keyword relevance)
      return popularAnswers;
    }

    // Default fallback
    return popularAnswers;
  }, [titleKey, matchingAnswers, popularAnswers, question]);

  // Create skeleton loading cards
  const renderSkeletonCards = () => {
    // Create an array of 4 skeleton cards
    return Array(4).fill(0).map((_, index) => (
      <div
        key={`skeleton-${index}`}
        className={`${styles.skeletonCard} ${isQuestionFormExpanded ? styles.skeletonCardShrink : ''}`}
      />
    ));
  };

  const handleScroll = () => {
    if (!scrollContainerRef.current) return;
    
    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
    setShowLeftArrow(scrollLeft > 0);
    setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
  };

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      // Check initial scroll state
      handleScroll();
    }
    
    return () => {
      scrollContainer?.removeEventListener('scroll', handleScroll);
    };
  }, [displayAnswers]);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -250, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 250, behavior: 'smooth' });
    }
  };

  if (initialLoadDone && (!displayAnswers || displayAnswers.length === 0)) {
    return <></>;
  }

  return (
    <div 
      className={styles.container}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div className={styles.headerContainer}>
        <h2 className={styles.title}>{t(titleKey)}</h2>
        {client?.clientType !== "Internal" && (
          <a
            href={`https://app.repd.us/${clientSubdomain}`}
            target="_blank"
            rel="noopener noreferrer"
            className={classNames(styles.seeMoreButton, clientId === "300" && styles.blackText)} // Black text for Pleasantville
            id="seeMoreButton"
          >
            {t('seeMoreVideos')}
          </a>
        )}
      </div>
      <div className={styles.scrollWrapper}>
        {showLeftArrow && isHovering && (
          <button 
            className={`${styles.navArrow} ${styles.leftArrow}`}
            onClick={scrollLeft}
            aria-label="Scroll left"
          >
            <ChevronLeft size={20} />
          </button>
        )}
        <div 
          ref={scrollContainerRef}
          className={styles.scrollContainer}
        >
          {isLoading || aiLoading ? (
            <div className={styles.skeletonContainer}>
              {renderSkeletonCards()}
            </div>
          ) : (
            <div className={styles.cardContainer}>
              {displayAnswers.map((answer) => (
                <VideoCard
                  key={answer.id}
                  answer={answer}
                  onClick={() => onVideoSelect(answer)}
                  isQuestionFormExpanded={isQuestionFormExpanded}
                />
              ))}
            </div>
          )}
        </div>
        {showRightArrow && isHovering && (
          <button 
            className={`${styles.navArrow} ${styles.rightArrow}`}
            onClick={scrollRight}
            aria-label="Scroll right"
          >
            <ChevronRight size={20} />
          </button>
        )}
      </div>
    </div>
  );
}
