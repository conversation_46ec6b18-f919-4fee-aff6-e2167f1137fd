{"_moduleAliases": {"@": "dist/src"}, "name": "marvin", "license": "MIT", "version": "0.2.0", "scripts": {"clean": "rm -rf dist", "build": "npm run clean && tsc", "start": "node -r module-alias/register dist/src/server.js", "parse-sitemap": "tsx src/scripts/parsing/parse_sitemap.ts", "parse-multiple-sitemaps": "tsx src/scripts/parsing/parse_multiple_sitemaps.ts", "test-parse": "tsx src/scripts/parsing/parse_example_page.ts", "test-question": "tsx src/scripts/test_question.ts", "toggle-site": "tsx src/scripts/sites/toggle_site.ts", "add-site": "tsx src/scripts/sites/add_site.ts", "list-sites": "tsx src/scripts/sites/list_sites.ts", "upload-files": "tsx src/scripts/files/upload_files_to_s3.ts", "lint": "eslint . --fix", "backup-db": "chmod +x ./db_backup.sh && ./db_backup.sh", "restore-db": "chmod +x ./db_restore.sh && ./db_restore.sh", "migrate": "tsx src/scripts/run_migration.ts", "create-embeddings": "tsx src/scripts/embeddings/create_embeddings_multiple.ts", "delete-site-data": "tsx src/scripts/sites/delete_site_data.ts", "add-specific-url": "tsx src/scripts/sites/add_specific_url.ts", "crawl-site-pages": "tsx src/scripts/parsing/crawl_site_pages.ts", "crawl-google-site-search": "tsx src/scripts/parsing/crawl_google_site_search.ts", "crawl-civicplus-search": "tsx src/scripts/parsing/crawl_civicplus_search.ts", "full-scrape": "chmod +x ./src/scheduler/full_scrape_process.sh && ./src/scheduler/full_scrape_process.sh", "scrape-unscraped": "tsx src/scheduler/run_scraping_modes.ts unscraped", "scrape-rescraped": "tsx src/scheduler/run_scraping_modes.ts rescraped", "scrape-priority": "tsx src/scheduler/run_scraping_modes.ts priority", "manage-priority": "tsx src/scheduler/manage_priority_links.ts", "update-js-requirements": "tsx src/scripts/sites/update_js_requirement.ts", "run-background-jobs": "tsx src/scripts/jobs/run-background-jobs.ts", "automated-scraping": "tsx src/scheduler/automated_scraping_with_jobs.ts", "migrate-answers": "tsx src/scripts/migration/migrate-answers.ts", "test-external-db": "tsx src/scripts/test/test-external-db.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.427.0", "@aws-sdk/s3-request-presigner": "^3.427.0", "@langchain/community": "^0.3.39", "@langchain/core": "^0.3.43", "@langchain/openai": "^0.5.4", "@types/cors": "^2.8.19", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "html-to-text": "^9.0.5", "langchain": "^0.3.20", "module-alias": "^2.2.3", "openai": "^4.28.0", "pdf-parse": "^1.1.1", "pg": "^8.14.1", "pg-hstore": "^2.3.4", "puppeteer": "^24.9.0", "sequelize": "^6.37.7", "tiktoken": "^1.0.15", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xml2js": "^0.6.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.23.0", "@types/cheerio": "^0.22.35", "@types/express": "^5.0.1", "@types/html-to-text": "^9.0.4", "@types/node": "^20.11.19", "@types/pg": "^8.11.0", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.5", "globals": "^16.0.0", "html2pug": "^4.0.0", "prettier": "^3.5.3", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.3", "typescript": "^5.3.3", "typescript-eslint": "^8.28.0"}}