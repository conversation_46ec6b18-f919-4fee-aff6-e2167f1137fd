# External Database Setup for Answer Model

This document explains how to set up and use the external database connection for the Answer model, which replaces the previous answer embeddings system.

## Overview

The system now supports two separate database connections:
- **Main Database**: Contains embeddings, sites, links, and other core data
- **External Database**: Contains the Answer model data (videos, transcriptions, etc.)

## Environment Variables

Add the following environment variable to your `.env` file:

```bash
# External database connection string for Answer model
# Format: postgresql://username:password@hostname:port/database_name
EXTERNAL_DATABASE_URL=postgresql://username:password@hostname:port/answers_db
```

## Database Schema

The external database will automatically create the following table:

```sql
CREATE TABLE answers (
  id SERIAL PRIMARY KEY,
  image_url VARCHAR,
  video_url VARCHAR NOT NULL,
  video_urls JSON NOT NULL,
  upload_id VARCHAR,
  video_key VARCHAR,
  video_duration FLOAT,
  subtitles JSO<PERSON>,
  transcription JSONB,
  transcription_translation JSONB,
  user_id BIGINT NOT NULL,
  client_id BIGINT NOT NULL,
  question_id BIGINT NOT NULL,
  is_draft BOOLEAN,
  is_approved BOOLEAN,
  is_denied BOOLEAN,
  is_shared BOOLEAN,
  is_pinned BOOLEAN,
  end_date TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## Usage

### Using the Answer Service

```typescript
import { answerService } from "@/services/answers/answerService";

// Find answers by client ID
const result = await answerService.findAnswersByClientId(clientId, {
  limit: 50,
  offset: 0,
  includeApproved: true,
  includeDrafts: false,
});

// Find answer by ID
const answer = await answerService.findAnswerById(answerId);

// Create new answer
const newAnswer = await answerService.createAnswer({
  videoUrl: "https://example.com/video.mp4",
  videoUrls: { hd: "https://example.com/video-hd.mp4" },
  clientId: 123,
  userId: 456,
  questionId: 789,
  transcription: { text: "Video transcription..." },
});

// Update answer
const updatedAnswer = await answerService.updateAnswer(answerId, {
  isApproved: true,
  isPinned: true,
});

// Search answers
const searchResults = await answerService.searchAnswers("search query", clientId);
```

### Using the Direct Model

```typescript
import { Answer, initExternalDb } from "@/store/models/external";

// Initialize external database connection
await initExternalDb();

// Use Sequelize model directly
const answers = await Answer.findAll({
  where: { clientId: 123 },
  limit: 10,
});
```

## API Endpoints

The system provides REST API endpoints for managing answers:

- `GET /api/answers?clientId=123` - Get answers by client ID
- `GET /api/answers/:id` - Get answer by ID
- `POST /api/answers` - Create new answer
- `PUT /api/answers/:id` - Update answer
- `DELETE /api/answers/:id` - Delete answer
- `GET /api/answers/search?q=query&clientId=123` - Search answers

## Migration from Answer Embeddings

To migrate existing data from the `answer_embeddings` table to the new `answers` table:

```bash
# Run migration for all clients
npm run migrate-answers

# Run migration for specific client
npm run migrate-answers -- --clientId=123

# Validate migration without running it
npm run migrate-answers -- --validate-only

# Run with custom batch size
npm run migrate-answers -- --batchSize=50
```

### Migration Script

```typescript
import { answerMigrationService } from "@/services/answers/migrationService";

// Migrate all answer embeddings
const result = await answerMigrationService.migrateAnswerEmbeddings();

// Migrate for specific client with custom batch size
const result = await answerMigrationService.migrateAnswerEmbeddings({
  clientId: 123,
  batchSize: 100,
});

// Validate migration
const validation = await answerMigrationService.validateMigration(clientId);
```

## Configuration

The external database connection uses the same configuration options as the main database:

- SSL is enabled in production
- Logging can be controlled via `LOG_SQL` environment variable
- Connection pooling and other Sequelize options are inherited

## Error Handling

The Answer service includes comprehensive error handling:

- Database connection errors are logged and re-thrown
- Invalid queries return appropriate HTTP status codes
- Migration errors are tracked and reported

## Performance Considerations

- The external database connection is initialized lazily
- Batch operations are used for migrations
- Indexes should be added based on query patterns
- Consider connection pooling for high-traffic scenarios

## Security

- Use separate database credentials for the external database
- Ensure proper network security between databases
- Consider encryption at rest for sensitive video data
- Implement proper access controls and authentication
